<template>
  <v-contextmenu ref="contextmenu">
    <v-contextmenu-item v-for="(item, index) in contextMenuData" :key="index" @click="contextMenuEvent(item.command)">
      {{ item.label }}
    </v-contextmenu-item>
  </v-contextmenu>
  <div class="scroll-container">
    <el-input
      v-model="filterText"
      style="width: 97%; padding-left: 6px; margin-top: 7px; margin-bottom: 2px"
      :placeholder="t('hmi.device.configureList.searchPlaceholder')"
      clearable
    />
    <div @mousedown="cancelContext">
      <el-tree
        ref="configureTreeRef"
        default-expand-all
        node-key="id"
        :data="configureTreeData"
        :current-node-key="currentNode"
        :highlight-current="true"
        :indent="20"
        :icon="ArrowRightBold"
        :accordion="false"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        @node-click="treeClick"
        @node-contextmenu="treeContextMenu"
      >
        <template #default="{ node, data }">
          <span class="custom-tree-node flx-start">
            <svg-icon v-if="data.svgIcon" :icon="data.svgIcon" :class="[data.type == 'hmi' ? 'tree-submenu' : 'tree-menu']" />
            <span v-contextmenu:contextmenu="contextmenu">{{ node.label }}</span>
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from "vue";
import { useHmiStore } from "@/stores/modules";
import { ResultData } from "@/api";
import { ArrowRightBold } from "@element-plus/icons-vue";
import { ElTree, ElMessageBox } from "element-plus";
import type Node from "element-plus/es/components/tree/src/model/node";
import { Message } from "@/scripts/message";
import { configureInfoApi } from "@/api/modules/biz/hmi";
import { Configure } from "@/api/interface/biz/hmi";
import mittBus from "@/utils/mittBus";
import { useI18n } from "vue-i18n";

const { t, locale } = useI18n();

type command = "project" | "custom" | "new" | "edit" | "rename" | "delete" | "open";
type MenuItem = {
  label: string;
  command: command;
};
let currTreeNode: Node;
const filterText = ref("");
const currentNode = ref();
const contextmenu = ref();
const contextMenuData = ref<MenuItem[]>();
const hmiStore = useHmiStore();
// 初始化为空数组，等待 initData 填充
const configureTreeData = ref<Configure.ConfigureInfo[]>([]);
const configureTreeRef = ref<InstanceType<typeof ElTree>>();

// 使用 computed 创建响应式的菜单项和根节点
const treeRoot = computed<Configure.ConfigureInfo>(() => ({
  id: "0",
  label: t("hmi.device.configureList.deviceMonitor"),
  svgIcon: "eva:layout-fill",
  type: "root"
}));

const treeMenuProjects = computed<MenuItem[]>(() => [
  { label: t("hmi.device.configureList.newProject"), command: "project" },
  { label: t("hmi.device.configureList.addCustomComponent"), command: "custom" }
]);

const treeMenuItems = computed<MenuItem[]>(() => [
  { label: t("hmi.device.configureList.newConfigure"), command: "new" },
  { label: t("hmi.device.configureList.renameProject"), command: "rename" },
  { label: t("hmi.device.configureList.deleteProject"), command: "delete" }
]);

const treeSubMenuItems = computed<MenuItem[]>(() => [
  { label: t("hmi.device.configureList.editConfigure"), command: "edit" },
  { label: t("hmi.device.configureList.renameConfigure"), command: "rename" },
  { label: t("hmi.device.configureList.deleteConfigure"), command: "delete" },
  { label: t("hmi.device.configureList.openFolder"), command: "open" }
]);

const treeContextMenu = async (_event: any, data: any, node: any): Promise<void> => {
  currTreeNode = node;
  currentNode.value = data.id;
  switch (data.type) {
    case "root":
      contextMenuData.value = treeMenuProjects.value;
      break;
    case "project":
      contextMenuData.value = treeMenuItems.value;
      break;
    case "hmi":
      contextMenuData.value = treeSubMenuItems.value;
      break;
  }
};

const cancelContext = (): void => {
  contextmenu.value.hide();
};

const treeClick = async (data: any, node: any) => {
  currTreeNode = node;
  currentNode.value = data.id;
  toCustom(false);
  toEdit(false);
  await hmiStore.setCurrentHmi(currTreeNode.data as Configure.ConfigureInfo);
};

const contextMenuEvent = (cmd: command): void => {
  switch (cmd) {
    case "project":
      newProject();
      break;
    case "new":
      newConfigure();
      break;
    case "rename":
      renameItem();
      break;
    case "delete":
      deleteItem();
      break;
    case "edit":
      toCustom(false);
      toEdit(true);
      break;
    case "custom":
      toEdit(false);
      toCustom(true);
      break;
    case "open":
      openDir();
      break;
    default:
      break;
  }
};

const newProject = (): void => {
  ElMessageBox.prompt(t("hmi.device.configureList.inputProjectName"), t("hmi.device.configureList.newProject"), {
    confirmButtonText: t("hmi.device.configureList.confirm"),
    cancelButtonText: t("hmi.device.configureList.cancel"),
    inputPattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,10}$/,
    inputErrorMessage: t("hmi.device.configureList.invalidName")
  }).then(async ({ value }) => {
    const project = {
      id: "",
      label: value,
      svgIcon: "eva:bookmark-fill",
      path: currTreeNode.data.label + "/" + value,
      type: "project"
    };
    const res: ResultData = await configureInfoApi.addConfigure(project);
    if (res.code == 0) {
      resetData(res);
      Message.success(t("hmi.device.configureList.projectAddSuccess", { name: value }));
      return;
    }
    Message.error(t("hmi.device.configureList.projectAddFailed", { name: value }) + res.msg);
  });
};

const newConfigure = (): void => {
  ElMessageBox.prompt(t("hmi.device.configureList.inputConfigureName"), t("hmi.device.configureList.newConfigure"), {
    confirmButtonText: t("hmi.device.configureList.confirm"),
    cancelButtonText: t("hmi.device.configureList.cancel"),
    inputPattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,10}$/,
    inputErrorMessage: t("hmi.device.configureList.invalidName")
  }).then(async ({ value }) => {
    const project = {
      id: currTreeNode.data.id,
      label: value,
      path: currTreeNode.parent.data.label + "/" + currTreeNode.data.label + "/" + value,
      svgIcon: "eva:image-fill",
      type: "hmi"
    };
    const res: ResultData = await configureInfoApi.addConfigure(project);
    if (res.code == 0) {
      resetData(res);
      Message.success(t("hmi.device.configureList.configureAddSuccess", { name: value }));
      return;
    }
    Message.error(t("hmi.device.configureList.configureAddFailed", { name: value }) + res.msg);
  });
};

const renameItem = (): void => {
  ElMessageBox.prompt(t("hmi.device.configureList.inputConfigureName"), t("hmi.device.configureList.renameConfigure"), {
    inputValue: currTreeNode.data.label,
    confirmButtonText: t("hmi.device.configureList.confirm"),
    cancelButtonText: t("hmi.device.configureList.cancel"),
    inputPattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,10}$/,
    inputErrorMessage: t("hmi.device.configureList.invalidName")
  }).then(async ({ value }) => {
    let path = currTreeNode.parent.data.label + "/" + value;
    if (currTreeNode.data.type == "hmi") {
      path = t("hmi.device.configureList.deviceMonitor") + "/" + currTreeNode.parent.data.label + "/" + value;
    }
    const project = {
      id: currTreeNode.data.id,
      pId: currTreeNode.parent.data.id,
      path: path,
      label: value,
      type: currTreeNode.data.type
    };
    const res: ResultData = await configureInfoApi.renameConfigure(project);
    if (res.code == 0) {
      resetData(res);
      Message.success(t("hmi.device.configureList.renameSuccess"));
      toEdit(false);
      await hmiStore.setCurrentHmi(undefined as unknown as Configure.ConfigureInfo);
      return;
    }
    Message.error(t("hmi.device.configureList.renameFailed") + res.msg);
  });
};

const deleteItem = (): void => {
  let msg = t("hmi.device.configureList.confirmDelete");
  if (currTreeNode.data.type == "project" && currTreeNode.getChildren()) {
    msg = t("hmi.device.configureList.confirmBatchDelete");
  }
  ElMessageBox.confirm(msg, t("hmi.device.configureList.remoteSet")).then(async () => {
    const project = {
      id: currTreeNode.data.id,
      pId: currTreeNode.parent.data.id,
      path: currTreeNode.data.path,
      type: currTreeNode.data.type
    };
    const res: ResultData = await configureInfoApi.removeConfigure(project);
    if (res.code == 0) {
      resetData(res);
      toEdit(false);
      Message.success(t("hmi.device.configureList.deleteSuccess"));
      await hmiStore.setCurrentHmi(undefined as unknown as Configure.ConfigureInfo);
      return;
    }
    Message.error(t("hmi.device.configureList.deleteFailed") + res.msg);
  });
};

const toCustom = (param: boolean): void => {
  mittBus.emit("toCustomConfigure", param);
};
const toEdit = (param: boolean): void => {
  if (param === true) {
    hmiStore.setCurrentHmi(currTreeNode.data as Configure.ConfigureInfo);
  }
  mittBus.emit("toEditConfigure", param);
};
const openDir = async () => {
  const param: Configure.openConfigureInfo = { path: currTreeNode.data.path };
  const res: ResultData = await configureInfoApi.openConfigureDir(param);
  if (res.code != 0) {
    Message.error(res.msg);
  }
};

const initData = async () => {
  const res: ResultData = await configureInfoApi.getConfigureList();
  // 无论是否有数据都要初始化根节点
  resetData(res);
};

const resetData = (res: ResultData) => {
  // 重新构建整个树数据
  const newTreeRoot = {
    ...treeRoot.value,
    children: res.data || []
  };

  // 直接设置整个树数据
  configureTreeData.value = [newTreeRoot];
};

const filterNode = (value: string, data: any): boolean => {
  if (!value) return true;
  return data.label?.includes(value) || false;
};

onMounted(async () => {
  await initData();
});

watch(filterText, val => {
  configureTreeRef.value!.filter(val);
});

// 监听语言变化，更新根节点标签
watch(locale, async () => {
  // 更新 configureTreeData 中的根节点，保持现有的子节点数据
  const rootIndex = configureTreeData.value.findIndex((item: Configure.ConfigureInfo) => item.type === "root");
  if (rootIndex !== -1) {
    const currentRoot = configureTreeData.value[rootIndex];
    // 保持现有的子节点数据，只更新标签
    configureTreeData.value[rootIndex] = {
      ...treeRoot.value,
      children: currentRoot.children
    };

    // 确保 DOM 更新后刷新树组件
    await nextTick();
    if (configureTreeRef.value) {
      // 强制刷新树组件的显示
      configureTreeRef.value.filter("");
    }
  }
});

mittBus.on("afterImportConfig", async type => (type != "device" ? await initData() : null));
</script>

<style lang="scss" scoped>
.scroll-container {
  position: relative;
  width: 100%;
  height: 70%;
  margin-top: 5px;
  overflow-y: auto;
  scrollbar-width: none;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  .custom-tree-node {
    flex: 1;
    font-size: 15px;
    span {
      flex: 1;
    }
    svg {
      font-size: 18px;
    }
    .tree-menu {
      margin-right: 2px;
      color: var(--el-color-primary);
    }
    .tree-submenu {
      color: var(--el-text-color);
    }
  }
}
</style>
