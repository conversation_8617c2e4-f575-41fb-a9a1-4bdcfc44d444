export default {
  device: {
    configure: {
      selectConfigure: "Veuillez sélectionner le fichier de symbole de configuration !",
      loading: "Chargement",
      operating: "Opération en cours",
      loadFailed: "Échec du chargement, raison :",
      getCustomDeviceFailed: "Échec de l'obtention de l'appareil personnalisé",
      registerDataFailed: "Échec de l'enregistrement des données, raison :",
      variablesNotExist: "Certaines variables n'existent pas :",
      getDataError: "Erreur d'obtention des données",
      remoteSet: "Télécommande",
      remoteControlFailed: "Échec de la télécommande, raison :",
      remoteControlSuccess: "Télécommande réussie",
      noRemoteControlType: "Type de télécommande non configuré",
      symbolChangeReload: "Rechargement après changement de symbole",
      deviceChangeReload: "Rechargement après changement d'appareil",
      deviceConnectReload: "Connexion d'appareil réussie, rechargement"
    },

    configureList: {
      searchPlaceholder: "Rechercher par mot-clé",
      deviceMonitor: "Surveillance d'appareil",
      newProject: "Nouveau projet",
      addCustomComponent: "Ajouter un composant personnalisé",
      newConfigure: "Nouvelle configuration",
      renameProject: "Renommer le projet",
      deleteProject: "Supprimer le projet",
      editConfigure: "Modifier la configuration",
      renameConfigure: "Renommer la configuration",
      deleteConfigure: "Supprimer la configuration",
      openFolder: "Ouvrir le dossier",
      inputProjectName: "Veuillez entrer le nom du projet (10 caractères maximum, pas de caractères spéciaux)",
      inputConfigureName: "Veuillez entrer le nom de la configuration (10 caractères maximum, pas de caractères spéciaux)",
      confirm: "Confirmer",
      cancel: "Annuler",
      invalidName: "Longueur ou contenu du nom invalide",
      projectAddSuccess: "Projet : {name} ajouté avec succès",
      projectAddFailed: "Projet : {name} échec de l'ajout, raison :",
      configureAddSuccess: "Configuration : {name} ajoutée avec succès",
      configureAddFailed: "Configuration : {name} échec de l'ajout, raison :",
      renameSuccess: "Renommage réussi",
      renameFailed: "Échec du renommage, raison :",
      confirmDelete: "Êtes-vous sûr de vouloir supprimer ?",
      confirmBatchDelete: "Ce projet contient des configurations associées, êtes-vous sûr de vouloir supprimer en lot ?",
      deleteSuccess: "Suppression réussie",
      deleteFailed: "Échec de la suppression, raison :"
    },
    configures: {
      customComponent: "Composant personnalisé",
      selectDevice: "Veuillez sélectionner l'appareil associé !",
      edit: "Modifier :"
    },
    customConfigure: {
      getCustomDeviceFailed: "Échec de l'obtention de l'appareil personnalisé",
      saveDeviceFailed: "Échec de l'enregistrement du symbole d'appareil",
      saveSuccess: "Enregistrement réussi",
      deleteDeviceFailed: "Échec de la suppression du symbole d'appareil",
      deleteSuccess: "Suppression réussie",
      tip: "Message d'information"
    },
    deviceList: {
      unnamed: "Appareil Sans Nom",
      connect: "Connecter",
      disconnect: "Déconnecter",
      edit: "Modifier",
      delete: "Supprimer",
      notFound: "Aucun appareil trouvé",
      editWarn: "Veuillez d'abord vous déconnecter avant de modifier",
      deleteWarn: "Veuillez d'abord vous déconnecter avant de supprimer",
      connectSuccess: "Appareil {name} : Connexion réussie",
      connectExist: "Appareil {name} : Connexion déjà existante",
      connectFailed: "Appareil {name} : Échec de connexion",
      connectFailedReason: "Raison de l'échec de connexion de l'appareil : {reason}",
      disconnectSuccess: "Appareil {name} : Déconnecté",
      operateFailed: "Appareil {name} : Échec de l'opération",
      remove: "Supprimer"
    },
    deviceSearch: {
      searchPlaceholder: "Rechercher un appareil"
    },
    editConfigure: {
      saveSuccess: "Enregistrement réussi",
      saveFailed: "Échec de l'enregistrement, raison :",
      loadFailed: "Échec du chargement, raison :",
      getCustomDeviceFailed: "Échec de l'obtention de l'appareil personnalisé",
      tip: "Message d'information"
    },
    remoteSet: {
      inputValue: "Valeur d'entrée",
      write: "Écrire",
      cancel: "Annuler",
      setFailed: "Échec de la télécommande, raison :",
      operateSuccess: "Opération réussie",
      noSetType: "Type de télécommande non configuré"
    }
  },
  graph: {
    component: {
      electricSymbols: "Symboles électriques",
      customComponents: "Composants personnalisés",
      basicComponents: "Composants de base"
    },
    toolbar: {
      undo: "Annuler",
      redo: "Rétablir",
      bringToFront: "Mettre au premier plan",
      sendToBack: "Mettre à l'arrière-plan",
      ratio: "Mise à l'échelle proportionnelle",
      delete: "Supprimer",
      save: "Enregistrer"
    },
    contextMenu: {
      group: "Grouper",
      ungroup: "Dégrouper",
      linkData: "Associer les données",
      equipmentSaddr: "Adresse de l'appareil"
    },
    dialog: {
      dataConfig: "Configuration des données",
      tip: "Message d'information",
      selectOneGraph: "Veuillez sélectionner un graphique"
    },
    message: {
      waitForCanvasInit: "Veuillez attendre l'initialisation du canevas",
      loadEquipmentFailed: "Échec du chargement du symbole",
      loadEquipmentError: "Échec du chargement de l'appareil",
      equipmentLoaded: "Chargement de l'appareil terminé"
    },
    basic: {
      title: "Composants de base",
      components: {
        line: "Ligne",
        text: "Texte",
        rectangle: "Rectangle",
        circle: "Cercle",
        ellipse: "Ellipse",
        triangle: "Triangle",
        arc: "Arc"
      }
    },
    selectEquipment: {
      sequence: "Index",
      name: "Nom",
      type: "Type",
      symbol: "Symbole",
      operation: "Opération",
      reference: "Référence"
    },
    setSAddr: {
      telemetry: "Télécommunication/Télémétrie",
      format: "Formatage",
      factor: "Facteur",
      remoteControl: "Télécommande",
      controlType: "Type de télécommande",
      controlValue: "Valeur de télécommande",
      remoteSet: "Télécommande",
      setType: "Type de télécommande",
      displayConfig: "Configuration d'affichage",
      addRow: "Ajouter une ligne",
      sequence: "Index",
      type: "Type",
      originalValue: "Valeur originale",
      displayValue: "Valeur d'affichage",
      operation: "Opération",
      text: "Texte",
      symbol: "Symbole",
      selectSymbol: "Sélectionner le symbole",
      confirm: "Confirmer",
      cancel: "Annuler",
      confirmDelete: "Êtes-vous sûr de vouloir supprimer ?",
      tip: "Message d'information",
      selectControl: "Contrôle sélectif",
      directControl: "Contrôle direct",
      controlClose: "Contrôle fermé",
      controlOpen: "Contrôle ouvert",
      cancelDelete: "Annuler la suppression"
    },
    equipmentType: {
      CBR: "Disjoncteur",
      DIS: "Sectionneur",
      GDIS: "Sectionneur de terre",
      PTR2: "Transformateur 2 enroulements",
      PTR3: "Transformateur 3 enroulements",
      VTR: "Transformateur de tension",
      CTR: "Transformateur de courant",
      EFN: "Dispositif de mise à la terre du neutre",
      IFL: "Ligne de sortie",
      EnergyConsumer: "Charge",
      GND: "Terre",
      Arrester: "Parafoudre",
      Capacitor_P: "Condensateur en parallèle",
      Capacitor_S: "Condensateur en série",
      Reactor_P: "Réactance en parallèle",
      Reactor_S: "Réactance en série",
      Ascoil: "Bobine d'extinction d'arc",
      Fuse: "Fusible",
      BAT: "Batterie",
      BSH: "Bushing",
      CAB: "Câble",
      LIN: "Ligne aérienne",
      GEN: "Générateur",
      GIL: "Ligne isolée au gaz",
      RRC: "Élément réactif rotatif",
      TCF: "Convertisseur de fréquence à commande thyristor",
      TCR: "Élément réactif à commande thyristor",
      LTC: "Changeur de prise",
      IND: "Inducteur"
    },
    equipmentName: {
      breaker_vertical: "Disjoncteur-vertical",
      breaker_horizontal: "Disjoncteur-horizontal",
      breaker_invalid_vertical: "Disjoncteur-invalide-vertical",
      breaker_invalid_horizontal: "Disjoncteur-invalide-horizontal",
      disconnector_vertical: "Sectionneur-vertical",
      disconnector_horizontal: "Sectionneur-horizontal",
      disconnector_invalid_vertical: "Sectionneur-invalide-vertical",
      disconnector_invalid_horizontal: "Sectionneur-invalide-horizontal",
      hv_fuse: "Fusible haute tension",
      station_transformer_2w: "Transformateur de poste (2 enroulements)",
      transformer_y_d_11: "Transformateur (Y/△-11)",
      transformer_d_y_11: "Transformateur (△/Y-11)",
      transformer_d_d: "Transformateur (△/△)",
      transformer_y_y_11: "Transformateur (Y/Y-11)",
      transformer_y_y_12_d_11: "Transformateur (Y/Y-12/△-11)",
      transformer_y_d_11_d_11: "Transformateur (Y/△-11/△-11)",
      transformer_y_y_v: "Transformateur (Y/Y/V)",
      transformer_autotransformer: "Transformateur (auto-transformateur)",
      voltage_transformer_2w: "Transformateur de tension (2 enroulements)",
      voltage_transformer_3w: "Transformateur de tension (3 enroulements)",
      voltage_transformer_4w: "Transformateur de tension (4 enroulements)",
      arrester: "Parafoudre",
      capacitor_horizontal: "Condensateur-horizontal",
      capacitor_vertical: "Condensateur-vertical",
      reactor: "Réactance",
      split_reactor: "Réactance divisée",
      power_inductor: "Inducteur de puissance",
      feeder: "Ligne de sortie",
      ground: "Terre",
      tap_changer: "Changeur de prise",
      connection_point: "Point de connexion",
      transformer_y_y_12_d_11_new: "Transformateur(Y/Y-12/△-11)(nouveau)",
      pt: "PT",
      arrester_new: "Parafoudre(nouveau)",
      disconnector_vertical_new: "Sectionneur-vertical(nouveau)",
      disconnector_horizontal_new: "Sectionneur-horizontal(nouveau)",
      arrester_new_vertical: "Parafoudre(nouveau)-vertical",
      disconnector_vertical_left_new: "Sectionneur-vertical-gauche(nouveau)"
    }
  },
  graphProperties: {
    blank: {
      propertySetting: "Paramètres de propriété"
    },
    graph: {
      canvasSetting: "Paramètres du canevas",
      grid: "Grille",
      backgroundColor: "Couleur d'arrière-plan"
    },
    group: {
      groupProperty: "Propriété de groupe",
      basic: "Base",
      width: "Largeur",
      height: "Hauteur",
      x: "Position(X)",
      y: "Position(Y)",
      angle: "Angle de rotation"
    },
    node: {
      nodeProperty: "Propriété de nœud",
      style: "Style",
      backgroundColor: "Couleur d'arrière-plan",
      borderWidth: "Largeur de bordure",
      borderColor: "Couleur de bordure",
      borderDasharray: "Style de bordure",
      rx: "Bordure rx",
      ry: "Bordure ry",
      position: "Position",
      width: "Largeur",
      height: "Hauteur",
      x: "Position(X)",
      y: "Position(Y)",
      property: "Propriété",
      angle: "Angle de rotation",
      zIndex: "Niveau(z)",
      fontFamily: "Police",
      fontColor: "Couleur de police",
      fontSize: "Taille de police",
      text: "Texte"
    },
    pathLine: {
      lineSetting: "Paramètres de ligne",
      style: "Style",
      lineHeight: "Largeur",
      lineColor: "Couleur",
      borderDasharray: "Bordure",
      position: "Position",
      width: "Largeur",
      height: "Hauteur",
      x: "Position(X)",
      y: "Position(Y)",
      property: "Propriété",
      angle: "Angle de rotation",
      zIndex: "Niveau(z)"
    }
  },
  business: {
    hmi: {
      title: "Gestion d'écran",
      form: {
        add: "Ajouter un écran",
        edit: "Modifier l'écran",
        view: "Voir l'écran",
        name: "Nom de l'écran",
        type: "Type d'écran",
        template: "Modèle d'écran",
        description: "Description",
        cancel: "Annuler",
        confirm: "Confirmer",
        validation: {
          name: "Veuillez entrer le nom de l'écran",
          type: "Veuillez sélectionner le type d'écran",
          template: "Veuillez sélectionner le modèle d'écran"
        }
      },
      columns: {
        name: "Nom de l'écran",
        type: "Type d'écran",
        template: "Modèle d'écran",
        createTime: "Date de création",
        updateTime: "Date de mise à jour",
        status: "Statut",
        operation: "Opération"
      },
      type: {
        device: "Écran d'appareil",
        process: "Écran de processus",
        alarm: "Écran d'alarme",
        custom: "Écran personnalisé"
      },
      status: {
        draft: "Brouillon",
        published: "Publié",
        archived: "Archivé"
      },
      editor: {
        title: "Édition d'écran",
        save: "Enregistrer",
        preview: "Aperçu",
        publish: "Publier",
        cancel: "Annuler",
        tools: {
          select: "Sélectionner",
          rectangle: "Rectangle",
          circle: "Cercle",
          line: "Ligne",
          text: "Texte",
          image: "Image",
          device: "Appareil",
          alarm: "Alarme",
          chart: "Graphique"
        },
        properties: {
          title: "Propriétés",
          position: "Position",
          size: "Taille",
          style: "Style",
          data: "Données",
          event: "Événement"
        }
      },
      preview: {
        title: "Aperçu d'écran",
        fullscreen: "Plein écran",
        exit: "Quitter",
        zoom: {
          in: "Zoom avant",
          out: "Zoom arrière",
          fit: "Adapter"
        }
      },
      publish: {
        title: "Publier l'écran",
        version: "Numéro de version",
        description: "Description de publication",
        cancel: "Annuler",
        confirm: "Confirmer",
        validation: {
          version: "Veuillez entrer le numéro de version",
          description: "Veuillez entrer la description de publication"
        }
      },
      template: {
        title: "Modèle d'écran",
        add: "Ajouter un modèle",
        edit: "Modifier le modèle",
        delete: "Supprimer le modèle",
        name: "Nom du modèle",
        category: "Catégorie de modèle",
        description: "Description",
        preview: "Aperçu",
        cancel: "Annuler",
        confirm: "Confirmer",
        validation: {
          name: "Veuillez entrer le nom du modèle",
          category: "Veuillez sélectionner la catégorie de modèle"
        }
      }
    }
  }
};
