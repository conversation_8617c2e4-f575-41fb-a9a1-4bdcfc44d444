<template>
  <div class="custom-configure-container">
    <div class="custom-configure-main" :style="configureMainStyle.mainRoot">
      <GraphDefine @get="getEquipment" @save="saveEquipment" @delete="deleteEquipment" ref="graphDefine"></GraphDefine>
    </div>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="ts">
import { useGlobalStore } from "@/stores/modules";
import { GraphDefine } from "@/views/biz/hmi/packages";
import { graphDefineApi } from "@/api/modules/biz/hmi";
import { EquipmentData } from "../../packages/graph";
import { useI18n } from "vue-i18n";
import ProgressDialog from "@/views/biz/debug/device/dialog/ProgressDialog.vue";

const { t } = useI18n();
const progressDialog = ref();
const globalStore = useGlobalStore();
const graphDefine = ref();

const configureMainStyle = computed<any>(() => {
  let offset = 98;
  if (globalStore.tabs) {
    offset += 40;
  }
  if (globalStore.footer) {
    offset += 30;
  }
  return { mainRoot: { height: `calc(100vh  - ${offset}px)` } };
});

const getEquipment = async () => {
  const result = await graphDefineApi.get();
  if (result.code != 0) {
    ElMessageBox.alert(t("hmi.device.customConfigure.getCustomDeviceFailed"), {
      title: t("hmi.device.customConfigure.tip"),
      type: "error"
    });
    return;
  }
  if (graphDefine && result.data) {
    const datas = result.data as EquipmentData[];
    graphDefine.value.equipmentListShow(datas);
  }
};
const saveEquipment = async (data: EquipmentData) => {
  const result = await graphDefineApi.save(JSON.stringify(data));
  if (result.code != 0) {
    let msg = t("hmi.device.customConfigure.saveDeviceFailed");
    if (result.msg) {
      msg += result.msg;
    }
    ElMessageBox.alert(msg, {
      title: t("hmi.device.customConfigure.tip"),
      type: "error"
    });
    return;
  } else {
    ElMessageBox.alert(t("hmi.device.customConfigure.saveSuccess"), {
      title: t("hmi.device.customConfigure.tip"),
      type: "info"
    });
  }
};
const deleteEquipment = async (data: EquipmentData) => {
  const result = await graphDefineApi.delete(data.id);
  if (result.code != 0) {
    let msg = t("hmi.device.customConfigure.deleteDeviceFailed");
    if (result.msg) {
      msg += result.msg;
    }
    ElMessageBox.alert(msg, {
      title: t("hmi.device.customConfigure.tip"),
      type: "error"
    });
    return;
  } else {
    ElMessageBox.alert(t("hmi.device.customConfigure.deleteSuccess"), {
      title: t("hmi.device.customConfigure.tip"),
      type: "info"
    });
    // 更新数据列表
    getEquipment();
  }
};
</script>

<style scoped lang="scss">
@import "@/styles/resize";
.custom-configure-container {
  flex-direction: row;
  width: 100%;
  .custom-configure-main {
    width: 100%;
  }
}
</style>
