<template>
  <div class="containerComponent">
    <el-collapse v-model="activeNames">
      <el-collapse-item v-for="(item, index) in componentArrs" name="common" :key="index">
        <template #title>
          {{ item.title }}
        </template>
        <el-row :gutter="10">
          <el-col
            v-for="(componentItem, index1) in item.components"
            :span="6"
            :key="index1"
            draggable="true"
            @dragstart="componentToolClick($event, componentItem.data)"
            class="graph-basic-item"
          >
            <el-image :src="componentItem.url" class="graph-equipment-img">
              <template #error>
                <span>{{ componentItem.title }}</span>
              </template>
            </el-image>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
    <el-form style="padding: 30px 5px 0 0">
      <el-form-item :label="t('graphDefine.graphComponent.deviceType')">
        <el-select v-model="form.type" @change="onTypeChange">
          <el-option v-for="item in equipmentTypeItems" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('graphDefine.graphComponent.deviceName')">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button style="margin-left: 60px" type="success" @click="onSave">{{ t("graphDefine.graphComponent.save") }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { Node } from "@antv/x6";
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { ComponentInfo, EquipmentType, EquipmentTypeMap, Select } from "../../..//graph/Graph";
import { getBasicComponent } from "../..";

const { t } = useI18n();

// 定义事件
const emit = defineEmits<{
  (e: "addComponent", event: MouseEvent, data: Node.Metadata): void;
  (e: "save", data: { name: string; type: EquipmentType }): void;
}>();
const form = ref<{
  type: EquipmentType;
  name: string;
}>({
  type: EquipmentType.CBR,
  name: EquipmentTypeMap.get(EquipmentType.CBR) as string
});
const items: Select<EquipmentType>[] = [];
for (const item of EquipmentTypeMap) {
  items.push({ label: item[1], value: item[0] });
}
const equipmentTypeItems = ref(items);

const activeNames = ref(["common"]);

const componentArrs: { title: string; components?: ComponentInfo[] }[] = [];
componentArrs.unshift(getBasicComponent(t));

const componentToolClick = (event: MouseEvent, data: unknown) => {
  // 创建节点
  const nodeData = data as any;
  const node: Node.Metadata = {
    ...nodeData
  };
  emit("addComponent", event, node);
};
const onTypeChange = () => {
  const name = EquipmentTypeMap.get(form.value.type);
  if (name) {
    form.value.name = name;
  }
};
const onSave = () => {
  emit("save", { name: form.value.name, type: form.value.type });
};
</script>
<style>
@import "../../../styles/Graph";
.containerComponent {
  padding: 0 0 0 10px;
}
</style>
