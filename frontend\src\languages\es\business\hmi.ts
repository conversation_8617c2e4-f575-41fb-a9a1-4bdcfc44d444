export default {
  device: {
    configure: {
      selectConfigure: "¡Por favor seleccione el archivo de símbolo de configuración!",
      loading: "Cargando",
      operating: "En operación",
      loadFailed: "Error al cargar, razón:",
      getCustomDeviceFailed: "Error al obtener dispositivo personalizado",
      registerDataFailed: "Error al registrar datos, razón:",
      variablesNotExist: "Algunas variables no existen:",
      getDataError: "Error al obtener datos",
      remoteSet: "Teleajuste",
      remoteControlFailed: "Telecontrol fallido, razón:",
      remoteControlSuccess: "Telecontrol exitoso",
      noRemoteControlType: "Tipo de telecontrol no configurado",
      symbolChangeReload: "Cambio de símbolo, recargar",
      deviceChangeReload: "Cambio de dispositivo, recargar",
      deviceConnectReload: "Dispositivo conectado con éxito, recargar"
    },

    configureList: {
      searchPlaceholder: "Buscar por palabra clave",
      deviceMonitor: "Monitor de dispositivo",
      newProject: "Nuevo proyecto",
      addCustomComponent: "Agregar componente personalizado",
      newConfigure: "Nueva configuración",
      renameProject: "Renombrar proyecto",
      deleteProject: "Eliminar proyecto",
      editConfigure: "Editar configuración",
      renameConfigure: "Renombrar configuración",
      deleteConfigure: "Eliminar configuración",
      openFolder: "Abrir carpeta contenedora",
      inputProjectName: "Por favor ingrese el nombre del proyecto (máx. 10 caracteres, sin caracteres especiales)",
      inputConfigureName: "Por favor ingrese el nombre de la configuración (máx. 10 caracteres, sin caracteres especiales)",
      confirm: "Confirmar",
      cancel: "Cancelar",
      invalidName: "Longitud o contenido del nombre no válido",
      projectAddSuccess: "Proyecto: {name} agregado exitosamente",
      projectAddFailed: "Proyecto: {name} agregado fallido, razón:",
      configureAddSuccess: "Configuración: {name} agregada exitosamente",
      configureAddFailed: "Configuración: {name} agregada fallida, razón:",
      renameSuccess: "Renombrado exitoso",
      renameFailed: "Renombrado fallido, razón:",
      confirmDelete: "¿Confirmar eliminación?",
      confirmBatchDelete: "Este proyecto tiene configuraciones asociadas, ¿confirmar eliminación por lotes?",
      deleteSuccess: "Eliminación exitosa",
      deleteFailed: "Eliminación fallida, razón:"
    },
    configures: {
      customComponent: "Componente personalizado",
      selectDevice: "¡Por favor seleccione el dispositivo asociado!",
      edit: "Editar:"
    },
    customConfigure: {
      getCustomDeviceFailed: "Error al obtener dispositivo personalizado",
      saveDeviceFailed: "Error al guardar símbolo del dispositivo",
      saveSuccess: "Guardado exitosamente",
      deleteDeviceFailed: "Error al eliminar símbolo del dispositivo",
      deleteSuccess: "Eliminación exitosa",
      tip: "Mensaje de sugerencia"
    },
    deviceList: {
      unnamed: "Dispositivo Sin Nombre",
      connect: "Conectar",
      disconnect: "Desconectar",
      edit: "Editar",
      delete: "Eliminar",
      notFound: "No se encontró el dispositivo",
      editWarn: "Por favor desconecte antes de editar",
      deleteWarn: "Por favor desconecte antes de eliminar",
      connectSuccess: "Dispositivo {name}: conectado exitosamente",
      connectExist: "Dispositivo {name}: conexión ya existe",
      connectFailed: "Dispositivo {name}: conexión fallida",
      connectFailedReason: "Razón de falla de conexión del dispositivo: {reason}",
      disconnectSuccess: "Dispositivo {name}: desconectado",
      operateFailed: "Dispositivo {name}: operación fallida",
      remove: "Eliminar"
    },
    deviceSearch: {
      searchPlaceholder: "Buscar dispositivo"
    },
    editConfigure: {
      saveSuccess: "Guardado exitosamente",
      saveFailed: "Guardado fallido, razón:",
      loadFailed: "Error al cargar, razón:",
      getCustomDeviceFailed: "Error al obtener dispositivo personalizado",
      tip: "Mensaje de sugerencia"
    },
    remoteSet: {
      inputValue: "Valor de entrada",
      write: "Escribir",
      cancel: "Cancelar",
      setFailed: "Teleajuste fallido, razón:",
      operateSuccess: "Operación exitosa",
      noSetType: "Tipo de teleajuste no configurado"
    }
  },
  graph: {
    component: {
      electricSymbols: "Símbolos eléctricos",
      customComponents: "Componentes personalizados",
      basicComponents: "Componentes básicos"
    },
    toolbar: {
      undo: "Deshacer",
      redo: "Rehacer",
      bringToFront: "Frente",
      sendToBack: "Atrás",
      ratio: "Escalado Proporcional",
      delete: "Eliminar",
      save: "Guardar"
    },
    contextMenu: {
      group: "Grupo",
      ungroup: "Desagrupar",
      linkData: "Datos asociados",
      equipmentSaddr: "Dirección del dispositivo"
    },
    dialog: {
      dataConfig: "Configuración de datos",
      tip: "Mensaje de sugerencia",
      selectOneGraph: "Por favor, seleccione un gráfico"
    },
    message: {
      waitForCanvasInit: "Por favor, espere a que se complete la inicialización del lienzo",
      loadEquipmentFailed: "Error al cargar gráfico",
      loadEquipmentError: "Error al cargar dispositivo",
      equipmentLoaded: "Dispositivo cargado completamente"
    },
    basic: {
      title: "Componentes básicos",
      components: {
        line: "Línea",
        text: "Texto",
        rectangle: "Rectángulo",
        circle: "Círculo",
        ellipse: "Elipse",
        triangle: "Triángulo",
        arc: "Arco"
      }
    },
    selectEquipment: {
      sequence: "Índice",
      name: "Nombre",
      type: "Tipo",
      symbol: "Símbolo",
      operation: "Operación",
      reference: "Referencia"
    },
    setSAddr: {
      telemetry: "Telemetría/Telemedida",
      format: "Formato",
      factor: "Factor",
      remoteControl: "Telecontrol",
      controlType: "Método de telecontrol",
      controlValue: "Valor de telecontrol",
      remoteSet: "Teleajuste",
      setType: "Método de teleajuste",
      displayConfig: "Configuración de visualización",
      addRow: "Agregar fila",
      sequence: "Índice",
      type: "Tipo",
      originalValue: "Valor original",
      displayValue: "Valor de visualización",
      operation: "Operación",
      text: "Texto",
      symbol: "Símbolo",
      selectSymbol: "Seleccionar símbolo",
      confirm: "Confirmar",
      cancel: "Cancelar",
      confirmDelete: "¿Confirmar eliminación?",
      tip: "Mensaje de sugerencia",
      selectControl: "Control de selección",
      directControl: "Control directo",
      controlClose: "Control de cierre",
      controlOpen: "Control de apertura",
      cancelDelete: "Cancelar eliminación"
    },
    equipmentType: {
      CBR: "Interruptor",
      DIS: "Corte de aislamiento",
      GDIS: "Corte de aislamiento de tierra",
      PTR2: "Transformador (Y/△-11)",
      PTR3: "Transformador (Y/△-11)",
      VTR: "Transformador de voltaje",
      CTR: "Transformador de corriente",
      EFN: "Dispositivo de aterrizaje del punto neutro",
      IFL: "Línea",
      EnergyConsumer: "Carga",
      GND: "Tierra",
      Arrester: "Pararrayos",
      Capacitor_P: "Capacitor en paralelo",
      Capacitor_S: "Capacitor en serie",
      Reactor_P: "Reactor en paralelo",
      Reactor_S: "Reactor en serie",
      Ascoil: "Bobina de compensación",
      Fuse: "Fusible",
      BAT: "Batería",
      BSH: "Tubo",
      CAB: "Cable",
      LIN: "Línea aérea",
      GEN: "Generador",
      GIL: "Línea de aislamiento eléctrico",
      RRC: "Componente rotativo de potencia reactiva",
      TCF: "Convertidor de control de frecuencia de tiristor",
      TCR: "Componente de control de potencia reactiva de tiristor",
      LTC: "Conmutador",
      IND: "Inductor"
    },
    equipmentName: {
      breaker_vertical: "Interruptor-vertical",
      breaker_horizontal: "Interruptor-horizontal",
      breaker_invalid_vertical: "Interruptor-inválido-vertical",
      breaker_invalid_horizontal: "Interruptor-inválido-horizontal",
      disconnector_vertical: "Corte-vertical",
      disconnector_horizontal: "Corte-horizontal",
      disconnector_invalid_vertical: "Corte-inválido-vertical",
      disconnector_invalid_horizontal: "Corte-inválido-horizontal",
      hv_fuse: "Fusible de alta tensión",
      station_transformer_2w: "Transformador de estación (dos devanados)",
      transformer_y_d_11: "Transformador (Y/△-11)",
      transformer_d_y_11: "Transformador (△/Y-11)",
      transformer_d_d: "Transformador (△/△)",
      transformer_y_y_11: "Transformador (Y/Y-11)",
      transformer_y_y_12_d_11: "Transformador (Y/Y-12/△-11)",
      transformer_y_d_11_d_11: "Transformador (Y/△-11/△-11)",
      transformer_y_y_v: "Transformador (Y/Y/V)",
      transformer_autotransformer: "Transformador (auto-transformador)",
      voltage_transformer_2w: "Transformador de voltaje (dos devanados)",
      voltage_transformer_3w: "Transformador de voltaje (tres devanados)",
      voltage_transformer_4w: "Transformador de voltaje (cuatro devanados)",
      arrester: "Pararrayos",
      capacitor_horizontal: "Capacitor-horizontal",
      capacitor_vertical: "Capacitor-vertical",
      reactor: "Reactor",
      split_reactor: "Reactor de división",
      power_inductor: "Inductor de potencia",
      feeder: "Línea",
      ground: "Tierra",
      tap_changer: "Conmutador",
      connection_point: "Punto de conexión",
      transformer_y_y_12_d_11_new: "Transformador (Y/Y-12/△-11) (nuevo)",
      pt: "PT",
      arrester_new: "Pararrayos (nuevo)",
      disconnector_vertical_new: "Corte-vertical (nuevo)",
      disconnector_horizontal_new: "Corte-horizontal (nuevo)",
      arrester_new_vertical: "Pararrayos (nuevo)-vertical",
      disconnector_vertical_left_new: "Corte-vertical izquierdo (nuevo)"
    }
  },
  graphProperties: {
    blank: {
      propertySetting: "Configuración de propiedad"
    },
    graph: {
      canvasSetting: "Configuración de lienzo",
      grid: "Cuadrícula",
      backgroundColor: "Color de fondo"
    },
    group: {
      groupProperty: "Propiedad de grupo",
      basic: "Básico",
      width: "Ancho",
      height: "Altura",
      x: "Posición (X)",
      y: "Posición (Y)",
      angle: "Ángulo de rotación"
    },
    node: {
      nodeProperty: "Propiedad de nodo",
      style: "Estilo",
      backgroundColor: "Color de fondo",
      borderWidth: "Ancho del borde",
      borderColor: "Color del borde",
      borderDasharray: "Estilo del borde",
      rx: "rx del borde",
      ry: "ry del borde",
      position: "Posición",
      width: "Ancho",
      height: "Altura",
      x: "Posición (X)",
      y: "Posición (Y)",
      property: "Propiedad",
      angle: "Ángulo de rotación",
      zIndex: "Nivel (z)",
      fontFamily: "Fuente",
      fontColor: "Color de fuente",
      fontSize: "Tamaño de fuente",
      text: "Texto"
    },
    pathLine: {
      lineSetting: "Configuración de línea",
      style: "Estilo",
      lineHeight: "Ancho",
      lineColor: "Color",
      borderDasharray: "Borde",
      position: "Posición",
      width: "Ancho",
      height: "Altura",
      x: "Posición (X)",
      y: "Posición (Y)",
      property: "Propiedad",
      angle: "Ángulo de rotación",
      zIndex: "Nivel (z)"
    }
  },
  business: {
    hmi: {
      title: "Gestión de pantallas",
      form: {
        add: "Nueva pantalla",
        edit: "Editar pantalla",
        view: "Ver pantalla",
        name: "Nombre de pantalla",
        type: "Tipo de pantalla",
        template: "Plantilla de pantalla",
        description: "Descripción",
        cancel: "Cancelar",
        confirm: "Confirmar",
        validation: {
          name: "Por favor ingrese el nombre de la pantalla",
          type: "Por favor seleccione el tipo de pantalla",
          template: "Por favor seleccione la plantilla de pantalla"
        }
      },
      columns: {
        name: "Nombre de pantalla",
        type: "Tipo de pantalla",
        template: "Plantilla de pantalla",
        createTime: "Tiempo de creación",
        updateTime: "Tiempo de actualización",
        status: "Estado",
        operation: "Operación"
      },
      type: {
        device: "Pantalla de dispositivo",
        process: "Pantalla de proceso",
        alarm: "Pantalla de alarma",
        custom: "Pantalla personalizada"
      },
      status: {
        draft: "Borrador",
        published: "Publicado",
        archived: "Archivado"
      },
      editor: {
        title: "Editor de pantalla",
        save: "Guardar",
        preview: "Vista previa",
        publish: "Publicar",
        cancel: "Cancelar",
        tools: {
          select: "Seleccionar",
          rectangle: "Rectángulo",
          circle: "Círculo",
          line: "Línea",
          text: "Texto",
          image: "Imagen",
          device: "Dispositivo",
          alarm: "Alarma",
          chart: "Gráfico"
        },
        properties: {
          title: "Propiedades",
          position: "Posición",
          size: "Tamaño",
          style: "Estilo",
          data: "Datos",
          event: "Evento"
        }
      },
      preview: {
        title: "Vista previa de pantalla",
        fullscreen: "Pantalla completa",
        exit: "Salir",
        zoom: {
          in: "Ampliar",
          out: "Reducir",
          fit: "Ajustar"
        }
      },
      publish: {
        title: "Publicar pantalla",
        version: "Número de versión",
        description: "Descripción de publicación",
        cancel: "Cancelar",
        confirm: "Confirmar",
        validation: {
          version: "Por favor ingrese el número de versión",
          description: "Por favor ingrese la descripción de publicación"
        }
      },
      template: {
        title: "Plantilla de pantalla",
        add: "Nueva plantilla",
        edit: "Editar plantilla",
        delete: "Eliminar plantilla",
        name: "Nombre de plantilla",
        category: "Categoría de plantilla",
        description: "Descripción",
        preview: "Vista previa",
        cancel: "Cancelar",
        confirm: "Confirmar",
        validation: {
          name: "Por favor ingrese el nombre de la plantilla",
          category: "Por favor seleccione la categoría de plantilla"
        }
      }
    }
  }
};
